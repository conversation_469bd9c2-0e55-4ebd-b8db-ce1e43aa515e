import 'dart:ffi';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import '../../../../widgets/avatar.dart';
import 'moments_media_grid.dart';
import 'moments_publish.dart';
import 'moments_vm.dart';
import 'custom_camera_page.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../../models/moments_list_response.dart';
import './chooseCover/choose_cover.dart';
import 'package:tencent_cloud_chat_demo/src/user_profile.dart';

class MomentsPage extends StatefulWidget {
  final V2TimUserFullInfo self;

  const MomentsPage({Key? key, required this.self}) : super(key: key);

  @override
  _MomentsPageState createState() => _MomentsPageState();
}

class _MomentsPageState extends State<MomentsPage>
    with TickerProviderStateMixin {
  final MomentsVm viewModel = MomentsVm();
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController = RefreshController();
  bool _showAppBarBackground = false;
  var page = 1;
  late final String _cachedAvatarUrl;
  bool _isExpanded = false;
  late AnimationController _controller;
  late Animation<double> _heightAnimation;

  // 底部输入框相关状态
  bool _showBottomInput = false;
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  MomentItem? _currentCommentMoment;
  CommentList? _currentReplyComment;

  // 每个朋友圈项目的展开状态管理
  final Map<String, bool> _expandedStates = {};

  @override
  void initState() {
    super.initState();
    _cachedAvatarUrl = widget.self.faceUrl ?? '';
    _scrollController.addListener(_onScroll);
    _getMomentsList('refresh');
    _preLoadImages();
    // 添加监听
    viewModel.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _heightAnimation = Tween<double>(begin: 240.0, end: 600.0).animate(
      CurvedAnimation(
          parent: _controller,
          curve: Curves.linear,
          reverseCurve: Curves.linear),
    );

    _controller.addListener(() {
      setState(() {});
    });

    // 添加焦点监听器，失焦时隐藏输入框
    _commentFocusNode.addListener(() {
      if (!_commentFocusNode.hasFocus && _showBottomInput) {
        // 延迟一点时间隐藏，避免点击发送按钮时立即隐藏
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!_commentFocusNode.hasFocus && mounted) {
            _hideCommentInput();
          }
        });
      }
    });
  }

  // 图片预先加载
  void _preLoadImages() {
    viewModel.momentsList.forEach((moment) {
      moment.mediaList?.forEach((media) {
        CachedNetworkImageProvider(media.mediaUrl!);
      });
    });
  }

  // 获取朋友圈列表
  Future<bool> _getMomentsList(String type) async {
    bool result = await viewModel.getMomentsList(type);
    return result;
  }

  void _onScroll() {
    // 当滚动位置超过AppLogo高度时，显示AppBar背景
    if (_scrollController.offset > 200 && !_showAppBarBackground) {
      setState(() {
        _showAppBarBackground = true;
      });
    } else if (_scrollController.offset <= 200 && _showAppBarBackground) {
      setState(() {
        _showAppBarBackground = false;
      });
    }
  }

  // 跳转发布页面
  void _goToPublishPage() {
    _showImagePickerDialog();
  }

  // 显示图片选择弹窗
  void _showImagePickerDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  child: _buildPickerOption(
                    title: TIM_t("拍摄"),
                    subtitle: TIM_t("照片或视频"),
                    onTap: () {
                      Navigator.pop(context);
                      _openCustomCamera();
                    },
                  ),
                ),

                Divider(
                  height: 1,
                  // 占的高度（包括线 + 上下间距）
                  thickness: 1,
                  // 线本身的粗细
                  color: Color(0xFFF2F2F2),
                  // 线的颜色
                  indent: 16,
                  // 左边缩进
                  endIndent: 16, // 右边缩进
                ),

                // 从手机相册选择
                _buildPickerOption(
                  title: TIM_t("从手机相册选择"),
                  subtitle: "",
                  onTap: () {
                    Navigator.pop(context);
                    _pickImageFromGallery();
                  },
                ),
                // Divider(
                //   height: 1,              // 占的高度（包括线 + 上下间距）
                //   thickness: 1,           // 线本身的粗细
                //   color: Color(0xFFF2F2F2),     // 线的颜色
                //   indent: 16,             // 左边缩进
                //   endIndent: 16,          // 右边缩进
                // ),
                // 朋友红包
                // _buildPickerOption(
                //   title: TIM_t("朋友撒红包"),
                //   subtitle: "",
                //   onTap: () {},
                // ),
                Container(
                  width: double.infinity, // 占满宽度
                  height: 10, // 高度 10
                  color: Color(0xFFF4F7F8), // 设置背景色
                ),
                // 取消按钮

                Material(
                  color: Colors.white,
                  child: InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      child: Center(
                        child: Text(
                          TIM_t("取消"),
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        );
      },
    );
  }

  // 构建选择器选项
  Widget _buildPickerOption({
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Center(
            child: Column(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 打开自定义相机
  Future<void> _openCustomCamera() async {
    try {
      // 先尝试简化的测试页面
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const CustomCameraPage(),
        ),
      );

      if (result != null && result is Map) {
        final String type = result['type'];
        final XFile file = result['file'];

        if (type == 'image' || type == 'video') {
          _navigateToPublishPage([file]);
        }
      }
    } catch (e) {
      debugPrint("打开自定义相机出错: $e");
      ToastUtils.toast(TIM_t("相机启动失败"));
    }
  }

  // 从相册选择媒体文件（图片和视频）
  Future<void> _pickImageFromGallery() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.media, // 选择媒体文件（图片和视频）
        allowMultiple: true, // 允许多选
        allowCompression: true, // 允许压缩
      );

      if (result != null && result.files.isNotEmpty) {
        // 将 PlatformFile 转换为 XFile
        List<XFile> selectedFiles = result.files.map((file) {
          return XFile(file.path!);
        }).toList();

        _navigateToPublishPage(selectedFiles);
      }
    } catch (e) {
      print("选择媒体文件出错: $e");
      ToastUtils.toast(TIM_t("选择媒体文件失败"));
    }
  }

  // 导航到发布页面
  void _navigateToPublishPage(List<XFile> selectedImages) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MomentsPublishPage(initialImages: selectedImages),
      ),
    ).then((onValue) {
      debugPrint('successsuccess$onValue');
      if (onValue != null && onValue is Map && onValue["success"] == true) {
        debugPrint('发布成功，刷新列表');
        // 显示加载指示器
        ToastUtils.showLoading();

        // 延时刷新
        Future.delayed(const Duration(milliseconds: 300), () {
          _getMomentsList("refresh").then((_) {
            ToastUtils.hideLoading();
          });
        });
      }
    });
  }

  // 下拉刷新
  void _onRefresh() async {
    await _getMomentsList("refresh");
    _refreshController.refreshCompleted();
  }

  // 上拉加载更多
  void _onLoading() async {
    bool result = await _getMomentsList("loadMore");
    if (result) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  // 获取用户资料
  Future<V2TimUserFullInfo> getUserInfo(String userId) async {
    V2TimValueCallback<List<V2TimUserFullInfo>> result =
        await TencentImSDKPlugin.v2TIMManager
            .getUsersInfo(userIDList: [userId]);

    if (result.code == 0) {
      // 获取成功
      List<V2TimUserFullInfo> userInfoList = result.data!;
      if (userInfoList.isNotEmpty) {
        V2TimUserFullInfo userInfo = userInfoList.first;
        return userInfo;
        // 其他用户信息...
      }
      throw Exception('未找到用户信息');
    } else {
      // 获取失败
      throw Exception('getUserInfo获取用户信息失败');
    }
  }

  // 根据id解析用户昵称
  Future<String> _getNickName(String phone) async {
    final res = await getUserInfo(phone);
    return res.nickName ?? '';
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _controller.dispose();
    _commentController.dispose();
    _commentFocusNode.dispose();

    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  // 显示底部输入框
  void _showCommentInput(MomentItem moment, [CommentList? replyComment]) {
    setState(() {
      _showBottomInput = true;
      _currentCommentMoment = moment;
      _currentReplyComment = replyComment;
    });

    // 延迟聚焦，确保输入框已经渲染
    Future.delayed(const Duration(milliseconds: 100), () {
      _commentFocusNode.requestFocus();
    });
  }

  // 隐藏底部输入框
  void _hideCommentInput() {
    setState(() {
      _showBottomInput = false;
      _currentCommentMoment = null;
      _currentReplyComment = null;
    });
    _commentController.clear();
    _commentFocusNode.unfocus();
  }

  // 发送评论
  void _sendComment() {
    final comment = _commentController.text.trim();
    if (comment.isEmpty || _currentCommentMoment == null) return;

    // 先失去焦点，避免键盘影响
    _commentFocusNode.unfocus();

    if (_currentReplyComment != null) {
      // 回复评论
      viewModel.comment(_currentCommentMoment!.id.toString(), comment,
          _currentReplyComment!.id.toString());

      final commentData = {
        'id': _currentCommentMoment!.id,
        'content': comment,
        'momentId': _currentCommentMoment!.id,
        'friendUserId': widget.self.userID,
        'friendNick': widget.self.nickName,
        'friendFaceUrl': widget.self.faceUrl,
        'replyCommentId': _currentReplyComment!.id,
        'name': widget.self.nickName,
        'replyName': _currentReplyComment!.name,
        'replyUserId': _currentReplyComment!.replyUserId,
        "replyNick": _currentReplyComment!.friendNick,
        "replyFaceUrl": _currentReplyComment!.friendFaceUrl,
      };

      final newComment = CommentList.fromJson(commentData);
      _currentCommentMoment!.commentList ??= [];
      _currentCommentMoment!.commentList!.add(newComment);
    } else {
      // 普通评论
      viewModel.comment(_currentCommentMoment!.id.toString(), comment, null);

      final commentData = {
        'id': _currentCommentMoment!.id,
        'content': comment,
        'momentId': _currentCommentMoment!.id,
        'name': widget.self.nickName,
        'friendUserId': widget.self.userID,
        'replyCommentId': 0,
        'friendNick': widget.self.nickName,
        'friendFaceUrl': widget.self.faceUrl,
      };

      final newComment = CommentList.fromJson(commentData);
      _currentCommentMoment!.commentList ??= [];
      _currentCommentMoment!.commentList!.add(newComment);
    }

    // 延迟隐藏输入框，确保失焦事件正确处理
    Future.delayed(const Duration(milliseconds: 50), () {
      _hideCommentInput();
    });
  }

  // 切换朋友圈项目的展开状态
  void _toggleMomentExpanded(String momentId) {
    // 先关闭其他已展开的项目
    _closeAllExpandedMoments(exceptId: momentId);

    final isExpanded = _expandedStates[momentId] ?? false;

    setState(() {
      _expandedStates[momentId] = !isExpanded;
    });
  }

  // 关闭所有展开的朋友圈项目
  void _closeAllExpandedMoments({String? exceptId}) {
    bool hasChanges = false;

    _expandedStates.forEach((id, isExpanded) {
      if (isExpanded && id != exceptId) {
        _expandedStates[id] = false;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      setState(() {});
    }
  }

  // 构建右侧操作区域（工具图标和展开的点赞评论组件）
  Widget _buildRightActionArea(
      MomentItem momentItem, V2TimUserFullInfo loginUserInfo) {
    final momentId = momentItem.id.toString();
    final isExpanded = _expandedStates[momentId] ?? false;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 自适应宽度的容器，溢出隐藏，用于动画效果
        ClipRect(
          child: AnimatedSize(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeOutCubic,
            child: isExpanded
                ? _buildExpandedActionRow(momentItem, loginUserInfo)
                : const SizedBox(width: 0, height: 32.0),
          ),
        ),
        // 工具图标（始终在最右边）
        GestureDetector(
          onTap: () {
            _toggleMomentExpanded(momentId);
          },
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(0, 4, 0, 4),
            child: Image.asset('assets/moments/icon_tools.png'),
          ),
        ),
      ],
    );
  }

  // 构建展开的操作行（点赞评论组件）
  Widget _buildExpandedActionRow(
      MomentItem momentItem, V2TimUserFullInfo loginUserInfo) {
    return GestureDetector(
      onTap: () {
        // 防止点击动画组件时关闭
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: 32.0,
        // 固定高度
        margin: const EdgeInsets.only(right: 4),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 6),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(0, 0, 0, 0.5),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IntrinsicWidth(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              LikeButton(
                moment: momentItem,
                self: widget.self,
                viewModel: viewModel,
              ),
              const SizedBox(width: 6),
              Container(
                height: 12,
                width: 1,
                color: const Color(0xffA8A8A8),
              ),
              const SizedBox(width: 4),
              _commentButton(momentItem, loginUserInfo),
            ],
          ),
        ),
      ),
    );
  }

  // 构建底部评论输入框
  Widget _buildBottomCommentInput() {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 8,
        bottom: MediaQuery.of(context).viewInsets.bottom + 8,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFE9E9E9), width: 1),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF6F6F6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  controller: _commentController,
                  focusNode: _commentFocusNode,
                  style: const TextStyle(fontSize: 14),
                  maxLines: 4,
                  minLines: 1,
                  decoration: InputDecoration(
                    hintText: _currentReplyComment != null
                        ? TIM_t("回复 ${_currentReplyComment!.friendNick}")
                        : TIM_t("请输入评论"),
                    hintStyle: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF999999),
                    ),
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onSubmitted: (_) => _sendComment(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: _sendComment,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF0072FC),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  TIM_t("发送"),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // 移除默认的appBar，使用自定义的透明appBar
      extendBodyBehindAppBar: true,
      // 允许body内容延伸到AppBar后面
      appBar: AppBar(
        backgroundColor:
            _showAppBarBackground ? Colors.white : Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios,
              color: _showAppBarBackground ? Colors.black : Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: _showAppBarBackground
            ? Text(TIM_t("朋友圈"),
                style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w500))
            : null,
        actions: [
          IconButton(
            icon: Image.asset('assets/moments/icon_add.png',
                width: 20,
                height: 20,
                color: _showAppBarBackground ? Colors.black : Colors.white),
            onPressed: () {
              _goToPublishPage();
            },
          ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          // 点击页面其他地方时隐藏输入框和关闭展开的组件
          if (_showBottomInput) {
            _commentFocusNode.unfocus();
          }
          _closeAllExpandedMoments();
        },
        child: SmartRefresher(
            controller: _refreshController,
            scrollController: _scrollController,
            enablePullDown: true,
            enablePullUp: viewModel.momentsList.isNotEmpty ? true : false,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            footer: _customFooter(),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _topView(),
                  // 这里添加朋友圈内容
                  _listView(),
                ],
              ),
            )),
      ),
      bottomNavigationBar: _showBottomInput ? _buildBottomCommentInput() : null,
    );
  }

  Widget _listView() {
    // if (momentsVm.isLoading && momentsVm.momentsList.isEmpty) {
    //   // 显示加载中状态
    //   return const Center(
    //     child: Column(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         CircularProgressIndicator(),
    //         SizedBox(height: 16),
    //         Text("加载中...", style: TextStyle(color: Colors.grey)),
    //       ],
    //     ),
    //   );
    // }

    // if (momentsVm.hasError && momentsVm.momentsList.isEmpty) {
    //   // 显示错误状态
    //   return Center(
    //     child: Column(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         Icon(Icons.error_outline, size: 48, color: Colors.grey),
    //         const SizedBox(height: 16),
    //         const Text("加载失败，请检查网络连接", style: TextStyle(color: Colors.grey)),
    //         const SizedBox(height: 24),
    //         ElevatedButton(
    //           onPressed: () => _getMomentsList('refresh'),
    //           child: const Text("重试"),
    //         ),
    //       ],
    //     ),
    //   );
    // }

    if (viewModel.momentsList.isEmpty) {
      // 显示空状态
      return Center(
        child:
            Text(TIM_t("暂无朋友圈内容"), style: const TextStyle(color: Colors.grey)),
      );
    }

    return Container(
        padding: const EdgeInsets.only(top: 32),
        child: ListView.builder(
          shrinkWrap: true,
          cacheExtent: MediaQuery.of(context).size.height * 5,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            return _itemView(viewModel.momentsList[index], widget.self, index);
          },
          itemCount: viewModel.momentsList.length,
        ));
  }

  Widget _itemView(
      MomentItem momentItem, V2TimUserFullInfo loginUserInfo, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: const EdgeInsets.only(bottom: 12),
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Color(0xffE9E9E9),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => UserProfile(
                            userID: momentItem.userId.toString(),
                            myId: widget.self.userID)));
              },
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: CachedNetworkImage(
                    imageUrl: momentItem.avatar ?? '',
                    width: 36,
                    height: 36,
                    fit: BoxFit.cover,
                    errorWidget: (context, error, stackTrace) {
                      return Avatar(
                        avatarUrl: momentItem.avatar ?? '',
                        size: 36,
                        radius: 18,
                        showBorder: true,
                        borderColor: Colors.white,
                        borderWidth: 2,
                      );
                    },
                  )),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    momentItem.name ?? '',
                    style: const TextStyle(
                        color: Color(0xff333333),
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  if(momentItem.content!='')
                  Text(
                    momentItem.content?? '',
                    style: const TextStyle(
                        color: Color(0xff333333),
                        fontSize: 12,
                        fontWeight: FontWeight.w400),
                    maxLines: 5,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                  _imageItemView(momentItem.mediaList!),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Text(
                              momentItem.createTime ?? '',
                              style: const TextStyle(
                                  color: Color(0xff999999), fontSize: 11),
                            ),
                            // 删除朋友圈图标 - 只有自己发送的才显示
                            if (momentItem.userId.toString() ==
                                widget.self.userID)
                              GestureDetector(
                                onTap: () =>
                                    _showDeleteConfirmDialog(momentItem),
                                child: Container(
                                  margin: const EdgeInsets.only(left: 2),
                                  padding: const EdgeInsets.all(4),
                                  child: const Icon(
                                    Icons.delete_outline,
                                    size: 16,
                                    color: Color(0xff999999),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      // 右侧区域：工具图标和展开的点赞评论组件
                      _buildRightActionArea(momentItem, loginUserInfo)
                    ],
                  ),
                  const SizedBox(height: 8),
                  if ((viewModel.momentsList[index].likeUserList?.isNotEmpty ??
                          false) ||
                      (viewModel.momentsList[index].commentList?.isNotEmpty ??
                          false))
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 6, horizontal: 8),
                      decoration: BoxDecoration(
                        color: const Color(0xffF0F0F0),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Column(
                        children: [
                          LikeList(
                              moment: viewModel.momentsList[index],
                              userId: loginUserInfo.userID!),
                          Container(
                              child: CommentListView(
                            moment: momentItem,
                            self: widget.self,
                            viewModel: viewModel,
                            userName: widget.self.nickName ?? '',
                          )),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

// 在moments_page.dart文件中添加以下代码
  Widget _customFooter() {
    return CustomFooter(
      builder: (context, mode) {
        Widget body;
        if (mode == LoadStatus.idle) {
          body = Text(TIM_t("上拉加载更多"),
              style: const TextStyle(color: Color(0xFF999999)));
        } else if (mode == LoadStatus.loading) {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xff07C160)),
                ),
              ),
              const SizedBox(width: 10),
              Text(TIM_t("加载中..."),
                  style: const TextStyle(color: Color(0xFF999999))),
            ],
          );
        } else if (mode == LoadStatus.failed) {
          body = Text(TIM_t("加载失败，点击重试"),
              style: const TextStyle(color: Color(0xFFFF5151)));
        } else if (mode == LoadStatus.canLoading) {
          body = Text(TIM_t("松开加载更多"),
              style: const TextStyle(color: Color(0xFF999999)));
        } else {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text("───", style: TextStyle(color: Color(0xFFDDDDDD))),
              const SizedBox(width: 10),
              Text(
                  TIM_t(""
                      ""),
                  style: const TextStyle(color: Color(0xFF999999))),
              const SizedBox(width: 10),
              const Text("───", style: TextStyle(color: Color(0xFFDDDDDD))),
            ],
          );
        }
        return SizedBox(
          height: 55.0,
          child: Center(child: body),
        );
      },
    );
  }

// 图片item
  Widget _imageItemView(List<MediaList>? mediaList) {
    // 如果媒体列表为空，返回空组件
    if (mediaList == null || mediaList.isEmpty) {
      return const SizedBox.shrink();
    }
    // 将MediaList对象转换为Map<String, Object>
    List<Map<String, Object>> mediaItems = mediaList
        .map((media) => {
              'mediaUrl': media.mediaUrl ?? '',
              'mediaType': media.mediaType ?? '',
              'sortOrder': media.sortOrder ?? 0
            })
        .toList();
    // 根据图片数量返回不同的布局
    return Column(
      children: [
        const SizedBox(height: 8),
        MomentsMediaGrid(mediaItems: mediaItems)
      ],
    );
  }

  // 评论按钮
  Widget _commentButton(MomentItem moment, V2TimUserFullInfo loginUserInfo) {
    return InkWell(
      onTap: () {
        _showCommentInput(moment);
      },
      child: Row(
        children: [
          Image.asset(
            'assets/moments/pl.png',
          ),
          const SizedBox(width: 2),
          Text(
            TIM_t("评论"),
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
    );
  }

  // 顶部功能
  Widget _topView() {
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    final statusBarHeight = mediaQuery.padding.top;
    // 使用 AnimatedBuilder 监听动画值变化
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        // 直接使用动画值计算高度
        const double minHeight = 240.0;
        final double maxHeight = _heightAnimation.value;
        final double currentHeight = _isExpanded
            ? minHeight + (maxHeight - minHeight) * _controller.value
            : maxHeight - (maxHeight - minHeight) * (1 - _controller.value);

        return Container(
          // 计算高度包含状态栏高度
          height: currentHeight + 16,
          child: Stack(
            children: [
              // 使用Positioned替代Container的负margin
              Positioned(
                top: -statusBarHeight,
                // 使用负的top值而不是负margin
                left: 0,
                right: 0,
                height: currentHeight + statusBarHeight,
                child: GestureDetector(
                  // onTap: _toggleExpanded,
                  child: Image.asset(
                    'assets/moments/moments_bg.png',
                    width: mediaQuery.size.width,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              // 用户信息行，根据展开状态显示或隐藏
              !_isExpanded
                  ? Positioned(
                      bottom: 0, // 将头像放在容器底部
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            widget.self.nickName ?? "",
                            style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(width: 8),
                          InkWell(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => UserProfile(
                                      userID: widget.self.userID.toString()),
                                ),
                              );
                            },
                            child: Avatar(
                              avatarUrl: _cachedAvatarUrl,
                              size: 60,
                              radius: 30,
                              showBorder: true,
                              borderColor: Colors.white,
                              borderWidth: 2,
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                      ))
                  : Positioned(
                      bottom: 32, // 将头像放在容器底部
                      right: 16,
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ChooseCoverPage(),
                            ),
                          );
                        },
                        child: Column(
                          children: [
                            Image.asset(
                              'assets/moments/cover.png',
                              width: 24,
                              height: 24,
                            ),
                            const Text(
                              '换封面',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 8),
                            )
                          ],
                        ),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }

  // 显示删除朋友圈确认弹窗
  void _showDeleteConfirmDialog(MomentItem momentItem) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 280,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
                  child: const Text(
                    '删除该朋友圈？',
                    style: TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff333333),
                    ),
                  ),
                ),
                // 分割线
                Container(
                  height: 0.5,
                  color: const Color(0xffE5E5E5),
                ),
                // 按钮区域
                Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Container(
                          height: 50,
                          alignment: Alignment.center,
                          child: const Text(
                            '取消',
                            style: TextStyle(
                              fontSize: 17,
                              color: Color(0xff333333),
                            ),
                          ),
                        ),
                      ),)
                    ),
                    // 竖直分割线
                    Container(
                      width: 0.5,
                      height: 50,
                      color: const Color(0xffE5E5E5),
                    ),
                    // 删除按钮
                    Expanded(
                        child: Material(
                          color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          _deleteMoment(momentItem);
                        },
                        child: Container(
                          height: 50,
                          alignment: Alignment.center,
                          child: const Text(
                            '删除',
                            style: TextStyle(
                              fontSize: 17,
                              color: Color(0xffFF3B30),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    )),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 删除朋友圈
  void _deleteMoment(MomentItem momentItem) {
    // TODO: 调用删除朋友圈接口
    // 示例接口调用：
    // viewModel.deleteMoment(momentItem.id.toString()).then((success) {
    //   if (success) {
    //     // 删除成功，从列表中移除
    //     setState(() {
    //       viewModel.momentsList.removeWhere((item) => item.id == momentItem.id);
    //     });
    //     ToastUtils.toast("删除成功");
    //   } else {
    //     ToastUtils.toast("删除失败");
    //   }
    // });

    // 临时处理：直接从列表中移除（实际项目中应该调用接口）
    setState(() {
      viewModel.momentsList.removeWhere((item) => item.id == momentItem.id);
    });
    ToastUtils.toast("删除成功");
  }
}

// 点赞按钮
class LikeButton extends StatefulWidget {
  final MomentItem moment;
  final V2TimUserFullInfo self;
  final MomentsVm viewModel;

  const LikeButton({
    Key? key,
    required this.moment,
    required this.self,
    required this.viewModel,
  }) : super(key: key);

  @override
  _LikeButtonState createState() => _LikeButtonState();
}

class _LikeButtonState extends State<LikeButton> {
  late bool isLike = false;

  @override
  void initState() {
    super.initState();
    widget.moment.likeUserList?.forEach((element) {
      if (element.friendUserId.toString() == widget.self.userID) {
        isLike = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (isLike) {
          var res =
              await widget.viewModel.cancelLike(widget.moment.id.toString());
          if (res) {
            setState(() {
              widget.moment.likeUserList ??= [];
              widget.moment.likeUserList!.removeWhere((element) =>
                  element.friendUserId.toString() == widget.self.userID);
              isLike = false;
            });
          }
        } else {
          debugPrint("点赞结果:${widget.moment.id}");
          var res =
              await widget.viewModel.likeMoment(widget.moment.id.toString());
          debugPrint("点赞结果:${widget.moment.id}:$res");
          if (res) {
            setState(() {
              widget.moment.likeUserList ??= [];
              final likeUserList = LikeUserList();
              likeUserList.friendUserId = int.parse(widget.self.userID ?? '0');
              likeUserList.friendNick = widget.self.nickName ?? "";
              likeUserList.friendFaceUrl = widget.self.faceUrl ?? "";

              widget.moment.likeUserList!.add(likeUserList);
              isLike = true;
            });
          }
        }
      },
      child: Row(children: [
        Image.asset(
          isLike ? 'assets/moments/like_active.png' : 'assets/moments/like.png',
        ),
        const SizedBox(
          width: 2,
        ),
        Text(
          TIM_t("赞"),
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ]),
    );
  }
}

// 点赞列表
class LikeList extends StatefulWidget {
  final MomentItem moment;
  final String userId;

  const LikeList({Key? key, required this.moment, required this.userId})
      : super(key: key);

  @override
  _LikeListState createState() => _LikeListState();
}

class _LikeListState extends State<LikeList> {
  List<String> nameList = [];

  @override
  void initState() {
    super.initState();
    // 拿到点赞列表
    if (widget.moment.likeUserList?.isNotEmpty ?? false) {
      setUserName();
    }
  }

  setUserName() async {
    nameList.clear();
    widget.moment.likeUserList!.forEach((element) {
      nameList.add(element.friendNick ?? '');
    });
    setState(() {});
  }

  @override
  void didUpdateWidget(LikeList oldWidget) {
    super.didUpdateWidget(oldWidget);
    setUserName();
  }

  @override
  Widget build(BuildContext context) {
    final likeUserList = widget.moment.likeUserList;
    if (likeUserList == null || likeUserList.isEmpty) {
      return Container();
    }

    // 评论人数
    final commentCount = widget.moment.commentList?.length ?? 0;

    // 限制显示的最大用户数
    final int maxDisplayUsers = 3;
    final int displayCount = likeUserList.length > maxDisplayUsers
        ? maxDisplayUsers
        : likeUserList.length;

    return Container(
        decoration: BoxDecoration(
            border: commentCount > 0
                ? const Border(
                    bottom: BorderSide(
                    color: Color(0xffE2E2E2),
                    width: 1,
                  ))
                : null),
        child: Row(children: [
          Image.asset('assets/moments/like_mini.png', width: 20, height: 20),
          const SizedBox(width: 2),
          Expanded(
              child: Text(
                  nameList.join(", ") +
                      (likeUserList.length > maxDisplayUsers
                          ? " 等${likeUserList.length}人"
                          : ""),
                  style:
                      const TextStyle(color: Color(0xff295386), fontSize: 12)))
        ]));
  }
}

// 评论列表
class CommentListView extends StatefulWidget {
  final MomentItem moment;
  final String userName;
  final MomentsVm viewModel;
  final V2TimUserFullInfo self;

  const CommentListView(
      {Key? key,
      required this.moment,
      required this.self,
      required this.userName,
      required this.viewModel})
      : super(key: key);

  @override
  _CommentListState createState() => _CommentListState();
}

class _CommentListState extends State<CommentListView> {
  // 用于跟踪每个评论的展开状态
  Map<int, bool> _expandedStates = {};

  @override
  void initState() {
    super.initState();
    // 拿到评论列表,然后循环设置评论人name
    if (widget.moment.commentList != null &&
        widget.moment.commentList!.isNotEmpty) {}
  }

  @override
  void didUpdateWidget(CommentListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.moment != oldWidget.moment) {
      // setUserInfo();
    }
  }

  // 回复评论
  handleReplyComment(CommentList comment) {
    debugPrint('comment: ${comment.toJson()}');
    // 找到父级的MomentsPage状态并调用显示输入框方法
    final momentsPageState =
        context.findAncestorStateOfType<_MomentsPageState>();
    momentsPageState?._showCommentInput(widget.moment, comment);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.moment.commentList!.isNotEmpty) const SizedBox(height: 3),
        ...List.generate(widget.moment.commentList?.length ?? 0, (index) {
          final comment = widget.moment.commentList![index];
          return InkWell(
              onTap: () {
                debugPrint('comment: ${comment.toJson()}');
                handleReplyComment(comment);
              },
              child: _buildCommentItem(comment));
        })
      ],
    );
  }

  Widget _buildCommentItem(CommentList comment) {
    final commentId = comment.id ?? 0;
    final isExpanded = _expandedStates[commentId] ?? false;
    final contentText = comment.content ?? '';
    final shouldShowExpandButton = contentText.length > 200;

    if (comment.replyCommentId != 0) {
      return Align(
        alignment: Alignment.centerLeft,
        child: RichText(
          textAlign: TextAlign.left,
          text: TextSpan(
            children: [
              TextSpan(
                text: comment.friendNick,
                style: const TextStyle(color: Color(0xff295386), fontSize: 14),
              ),
              const TextSpan(
                text: "回复",
                style: TextStyle(color: Color(0xff333333), fontSize: 14),
              ),
              TextSpan(
                text: comment.replyNick,
                style: const TextStyle(color: Color(0xff295386), fontSize: 14),
              ),
              const TextSpan(
                text: ": ",
                style: TextStyle(color: Color(0xff333333), fontSize: 14),
              ),
              TextSpan(
                text: shouldShowExpandButton && !isExpanded
                    ? '${contentText.substring(0, 200)}...'
                    : contentText,
                style: const TextStyle(color: Color(0xff333333), fontSize: 14),
              ),
              if (shouldShowExpandButton)
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _expandedStates[commentId] = !isExpanded;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(left: 4),
                      child: Text(
                        isExpanded ? '折叠' : '展开',
                        style: const TextStyle(
                          color: Color(0xff1E90FF),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          softWrap: true,
          overflow: TextOverflow.visible,
        ),
      );
    }

    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.left,
        text: TextSpan(
          children: [
            TextSpan(
              text: comment.friendNick,
              style: const TextStyle(color: Color(0xff295386), fontSize: 14),
            ),
            const TextSpan(
              text: ": ",
              style: TextStyle(color: Color(0xff333333), fontSize: 14),
            ),
            TextSpan(
              text: shouldShowExpandButton && !isExpanded
                  ? '${contentText.substring(0, 200)}...'
                  : contentText,
              style: const TextStyle(color: Color(0xff333333), fontSize: 14),
            ),
            if (shouldShowExpandButton)
              WidgetSpan(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _expandedStates[commentId] = !isExpanded;
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 4),
                    child: Text(
                      isExpanded ? '折叠' : '展开',
                      style: const TextStyle(
                        color: Color(0xff1E90FF),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
        softWrap: true,
        overflow: TextOverflow.visible,
      ),
    );
  }
}
